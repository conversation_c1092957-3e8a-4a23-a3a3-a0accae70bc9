export interface Dimensions {
  length: number;
  width: number;
  height: number;
}

export interface Product {
  id?: string;
  name: string;
  dimensions: Dimensions;
  weight: number;
  shape: 'rectangular' | 'cylindrical' | 'spherical' | 'irregular';
  fragility: 'low' | 'medium' | 'high' | 'very_high';
  value?: number;
  special_requirements?: string[];
}

export interface PackagingRequest {
  products: Product[];
  shipping_distance?: number;
  priority: 'cost' | 'speed' | 'environmental' | 'balanced';
  custom_constraints?: Record<string, any>;
}

export interface OptimizedPackaging {
  box_dimensions: Dimensions;
  material_type: string;
  cushioning_thickness: number;
  product_arrangement: ProductArrangement[];
  estimated_cost: number;
  material_usage: number;
  empty_space_percentage: number;
  environmental_score: number;
  protection_level: number;
}

export interface ProductArrangement {
  product_id: string;
  position: {
    x: number;
    y: number;
    z: number;
  };
  rotation: {
    x: number;
    y: number;
    z: number;
  };
}

export interface EnvironmentalImpact {
  carbon_footprint: number;
  material_waste_reduction: number;
  recyclability_score: number;
  eco_friendly_materials_used: boolean;
  estimated_decomposition_time?: number;
}

export interface ARVisualizationData {
  box: {
    dimensions: Dimensions;
    material: string;
    color: string;
  };
  products: ProductArrangement[];
  cushioning: {
    thickness: number;
    type: string;
    color: string;
  };
  metrics: {
    empty_space: number;
    protection_level: number;
    environmental_score: number;
  };
}

export interface StandardBox {
  name: string;
  dimensions: Dimensions;
  max_weight: number;
  cost_per_unit: number;
  availability: boolean;
}
