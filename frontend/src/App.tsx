import React, { useState } from 'react';
import {
  Container,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Box,
  Alert,
  Snackbar
} from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { OptimizeOutlined, ViewInArOutlined } from '@mui/icons-material';

import ProductForm from './components/ProductForm';
import OptimizationResults from './components/OptimizationResults';
import ARViewer from './components/ARViewer';
import { packagingAPI } from './services/api';
import {
  Product,
  PackagingRequest,
  OptimizedPackaging,
  EnvironmentalImpact,
  ARVisualizationData
} from './types/packaging';

const theme = createTheme({
  palette: {
    primary: {
      main: '#2196f3',
    },
    secondary: {
      main: '#4caf50',
    },
  },
});

function App() {
  const [products, setProducts] = useState<Product[]>([]);
  const [priority, setPriority] = useState<string>('balanced');
  const [shippingDistance, setShippingDistance] = useState<number>(0);
  const [optimizedPackaging, setOptimizedPackaging] = useState<OptimizedPackaging | null>(null);
  const [environmentalImpact, setEnvironmentalImpact] = useState<EnvironmentalImpact | null>(null);
  const [arVisualizationData, setArVisualizationData] = useState<ARVisualizationData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleOptimize = async () => {
    if (products.length === 0) {
      setError('Please add at least one product');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const request: PackagingRequest = {
        products,
        priority: priority as any,
        shipping_distance: shippingDistance > 0 ? shippingDistance : undefined
      };

      // Optimize packaging
      const packaging = await packagingAPI.optimizePackaging(request);
      setOptimizedPackaging(packaging);

      // Calculate environmental impact
      const impact = await packagingAPI.calculateEnvironmentalImpact(
        packaging,
        shippingDistance
      );
      setEnvironmentalImpact(impact);

      // Get AR visualization data
      const arData = await packagingAPI.getARVisualizationData(packaging);
      setArVisualizationData(arData);

      setSuccess('Packaging optimized successfully!');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Optimization failed');
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setError(null);
    setSuccess(null);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4, textAlign: 'center' }}>
          <Typography variant="h3" component="h1" gutterBottom>
            AI-Powered Packaging Optimization
          </Typography>
          <Typography variant="h6" color="textSecondary" gutterBottom>
            Minimize waste, reduce costs, and protect the environment with intelligent packaging design
          </Typography>
        </Box>

        <Grid container spacing={3}>
          {/* Left Column - Input Forms */}
          <Grid item xs={12} lg={6}>
            {/* Product Form */}
            <Box sx={{ mb: 3 }}>
              <ProductForm
                products={products}
                onProductsChange={setProducts}
              />
            </Box>

            {/* Optimization Settings */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Optimization Settings
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Priority</InputLabel>
                      <Select
                        value={priority}
                        onChange={(e) => setPriority(e.target.value)}
                      >
                        <MenuItem value="cost">Cost Optimization</MenuItem>
                        <MenuItem value="environmental">Environmental Impact</MenuItem>
                        <MenuItem value="balanced">Balanced Approach</MenuItem>
                        <MenuItem value="speed">Fast Shipping</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Shipping Distance (km)"
                      type="number"
                      value={shippingDistance}
                      onChange={(e) => setShippingDistance(parseFloat(e.target.value) || 0)}
                      helperText="Optional: for environmental impact calculation"
                    />
                  </Grid>
                </Grid>

                <Box sx={{ mt: 3 }}>
                  <Button
                    variant="contained"
                    size="large"
                    startIcon={<OptimizeOutlined />}
                    onClick={handleOptimize}
                    disabled={loading || products.length === 0}
                    fullWidth
                  >
                    {loading ? 'Optimizing...' : 'Optimize Packaging'}
                  </Button>
                </Box>
              </CardContent>
            </Card>

            {/* Optimization Results */}
            <OptimizationResults
              packaging={optimizedPackaging}
              environmentalImpact={environmentalImpact}
              loading={loading}
            />
          </Grid>

          {/* Right Column - Visualization */}
          <Grid item xs={12} lg={6}>
            <ARViewer
              visualizationData={arVisualizationData}
              onEnterAR={() => console.log('Entering AR mode')}
            />

            {/* Additional Information */}
            {optimizedPackaging && (
              <Card sx={{ mt: 3 }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Optimization Summary
                  </Typography>
                  
                  <Alert severity="success" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      <strong>Space Efficiency:</strong> {(100 - optimizedPackaging.empty_space_percentage).toFixed(1)}% 
                      of box volume is utilized effectively.
                    </Typography>
                  </Alert>

                  {environmentalImpact && (
                    <Alert severity="info" sx={{ mb: 2 }}>
                      <Typography variant="body2">
                        <strong>Environmental Benefit:</strong> {environmentalImpact.material_waste_reduction.toFixed(1)}% 
                        less material waste compared to standard packaging.
                      </Typography>
                    </Alert>
                  )}

                  <Typography variant="body2" color="textSecondary">
                    This optimization considers product dimensions, fragility requirements, 
                    material properties, and environmental impact to provide the best packaging solution.
                  </Typography>
                </CardContent>
              </Card>
            )}
          </Grid>
        </Grid>

        {/* Snackbar for notifications */}
        <Snackbar
          open={!!error}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
        >
          <Alert onClose={handleCloseSnackbar} severity="error">
            {error}
          </Alert>
        </Snackbar>

        <Snackbar
          open={!!success}
          autoHideDuration={4000}
          onClose={handleCloseSnackbar}
        >
          <Alert onClose={handleCloseSnackbar} severity="success">
            {success}
          </Alert>
        </Snackbar>
      </Container>
    </ThemeProvider>
  );
}

export default App;
