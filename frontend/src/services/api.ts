import axios from 'axios';
import {
  PackagingRequest,
  OptimizedPackaging,
  EnvironmentalImpact,
  ARVisualizationData,
  StandardBox
} from '../types/packaging';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const packagingAPI = {
  // Optimize packaging
  optimizePackaging: async (request: PackagingRequest): Promise<OptimizedPackaging> => {
    const response = await api.post('/api/optimize', request);
    return response.data;
  },

  // Calculate environmental impact
  calculateEnvironmentalImpact: async (
    packaging: OptimizedPackaging,
    shippingDistance: number = 0
  ): Promise<EnvironmentalImpact> => {
    const response = await api.post('/api/environmental-impact', packaging, {
      params: { shipping_distance: shippingDistance }
    });
    return response.data;
  },

  // Get available materials
  getAvailableMaterials: async () => {
    const response = await api.get('/api/materials');
    return response.data;
  },

  // Get standard boxes
  getStandardBoxes: async (): Promise<StandardBox[]> => {
    const response = await api.get('/api/standard-boxes');
    return response.data;
  },

  // Get AR visualization data
  getARVisualizationData: async (packaging: OptimizedPackaging): Promise<ARVisualizationData> => {
    const response = await api.post('/api/ar/visualization-data', packaging);
    return response.data;
  },

  // Batch optimize
  batchOptimize: async (requests: PackagingRequest[]) => {
    const response = await api.post('/api/batch-optimize', requests);
    return response.data;
  },

  // Get eco recommendations
  getEcoRecommendations: async (packaging: OptimizedPackaging) => {
    const response = await api.get('/api/eco-recommendations', {
      data: packaging
    });
    return response.data;
  },

  // Calculate cumulative impact
  calculateCumulativeImpact: async (
    impacts: EnvironmentalImpact[],
    timePeriodDays: number = 365
  ) => {
    const response = await api.post('/api/cumulative-impact', impacts, {
      params: { time_period_days: timePeriodDays }
    });
    return response.data;
  },

  // Health check
  healthCheck: async () => {
    const response = await api.get('/api/health');
    return response.data;
  }
};

// Error handling interceptor
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    throw error;
  }
);

export default api;
