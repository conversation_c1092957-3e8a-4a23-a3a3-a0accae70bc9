import React, { Suspense, useRef, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Box, Text, Html } from '@react-three/drei';
import { XR, Controllers, Hands } from '@react-three/xr';
import { Box as ThreeBox, Mesh } from 'three';
import {
  Card,
  CardContent,
  Typography,
  Button,
  Box as MuiBox,
  Alert,
  CircularProgress
} from '@mui/material';
import { ARVisualizationData } from '../types/packaging';

interface ARViewerProps {
  visualizationData: ARVisualizationData | null;
  onEnterAR?: () => void;
}

// 3D Box Component
const PackagingBox: React.FC<{ data: ARVisualizationData }> = ({ data }) => {
  const meshRef = useRef<Mesh>(null);
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime) * 0.1;
    }
  });

  const { box, products, cushioning } = data;
  const { length, width, height } = box.dimensions;

  return (
    <group>
      {/* Main packaging box */}
      <Box
        ref={meshRef}
        args={[length / 10, height / 10, width / 10]} // Scale down for display
        position={[0, 0, 0]}
      >
        <meshStandardMaterial
          color={box.color}
          transparent
          opacity={0.7}
          wireframe={false}
        />
      </Box>

      {/* Box wireframe for better visibility */}
      <Box
        args={[length / 10, height / 10, width / 10]}
        position={[0, 0, 0]}
      >
        <meshBasicMaterial
          color="#333333"
          wireframe
          transparent
          opacity={0.3}
        />
      </Box>

      {/* Products inside the box */}
      {products.map((product, index) => (
        <Box
          key={product.product_id}
          args={[0.5, 0.3, 0.3]} // Simplified product representation
          position={[
            (product.position.x - length / 2) / 10,
            (product.position.z - height / 2) / 10,
            (product.position.y - width / 2) / 10
          ]}
        >
          <meshStandardMaterial color="#4CAF50" />
        </Box>
      ))}

      {/* Cushioning representation */}
      {cushioning.thickness > 0 && (
        <Box
          args={[
            (length + cushioning.thickness * 2) / 10,
            (height + cushioning.thickness * 2) / 10,
            (width + cushioning.thickness * 2) / 10
          ]}
          position={[0, 0, 0]}
        >
          <meshStandardMaterial
            color={cushioning.color}
            transparent
            opacity={0.2}
          />
        </Box>
      )}

      {/* Information labels */}
      <Text
        position={[0, (height / 10) / 2 + 1, 0]}
        fontSize={0.3}
        color="#333333"
        anchorX="center"
        anchorY="middle"
      >
        {`${length}×${width}×${height} cm`}
      </Text>

      <Text
        position={[0, (height / 10) / 2 + 0.5, 0]}
        fontSize={0.2}
        color="#666666"
        anchorX="center"
        anchorY="middle"
      >
        {`Empty Space: ${data.metrics.empty_space.toFixed(1)}%`}
      </Text>
    </group>
  );
};

// Loading component
const LoadingSpinner: React.FC = () => (
  <Html center>
    <CircularProgress />
  </Html>
);

// Main AR Viewer Component
const ARViewer: React.FC<ARViewerProps> = ({ visualizationData, onEnterAR }) => {
  const [isARSupported, setIsARSupported] = useState<boolean | null>(null);
  const [isARActive, setIsARActive] = useState(false);

  React.useEffect(() => {
    // Check WebXR support
    if ('xr' in navigator) {
      navigator.xr?.isSessionSupported('immersive-ar').then(setIsARSupported);
    } else {
      setIsARSupported(false);
    }
  }, []);

  const handleEnterAR = () => {
    setIsARActive(true);
    onEnterAR?.();
  };

  const handleExitAR = () => {
    setIsARActive(false);
  };

  if (!visualizationData) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            3D Packaging Visualization
          </Typography>
          <Alert severity="info">
            Optimize packaging first to see 3D visualization
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          3D Packaging Visualization
        </Typography>
        
        {/* AR Controls */}
        <MuiBox sx={{ mb: 2, display: 'flex', gap: 1 }}>
          {isARSupported && (
            <Button
              variant="contained"
              color="primary"
              onClick={handleEnterAR}
              disabled={isARActive}
            >
              Enter AR Mode
            </Button>
          )}
          
          {isARActive && (
            <Button
              variant="outlined"
              onClick={handleExitAR}
            >
              Exit AR
            </Button>
          )}
          
          {isARSupported === false && (
            <Alert severity="warning" sx={{ width: '100%' }}>
              AR not supported on this device. Showing 3D preview instead.
            </Alert>
          )}
        </MuiBox>

        {/* 3D Canvas */}
        <MuiBox sx={{ height: 400, border: '1px solid #ddd', borderRadius: 1 }}>
          <Canvas
            camera={{ position: [5, 5, 5], fov: 60 }}
            style={{ width: '100%', height: '100%' }}
          >
            <XR
              onSessionStart={handleEnterAR}
              onSessionEnd={handleExitAR}
            >
              <ambientLight intensity={0.6} />
              <pointLight position={[10, 10, 10]} intensity={0.8} />
              <directionalLight position={[-10, -10, -5]} intensity={0.5} />
              
              <Suspense fallback={<LoadingSpinner />}>
                <PackagingBox data={visualizationData} />
              </Suspense>
              
              <OrbitControls
                enablePan={true}
                enableZoom={true}
                enableRotate={true}
                maxDistance={20}
                minDistance={2}
              />
              
              <Controllers />
              <Hands />
            </XR>
          </Canvas>
        </MuiBox>

        {/* Metrics Display */}
        <MuiBox sx={{ mt: 2, display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: 1 }}>
          <MuiBox sx={{ textAlign: 'center', p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
            <Typography variant="body2" color="textSecondary">
              Empty Space
            </Typography>
            <Typography variant="h6">
              {visualizationData.metrics.empty_space.toFixed(1)}%
            </Typography>
          </MuiBox>
          
          <MuiBox sx={{ textAlign: 'center', p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
            <Typography variant="body2" color="textSecondary">
              Protection Level
            </Typography>
            <Typography variant="h6">
              {visualizationData.metrics.protection_level.toFixed(0)}%
            </Typography>
          </MuiBox>
          
          <MuiBox sx={{ textAlign: 'center', p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
            <Typography variant="body2" color="textSecondary">
              Eco Score
            </Typography>
            <Typography variant="h6">
              {visualizationData.metrics.environmental_score.toFixed(0)}%
            </Typography>
          </MuiBox>
        </MuiBox>

        {/* Instructions */}
        <Alert severity="info" sx={{ mt: 2 }}>
          <Typography variant="body2">
            <strong>3D Controls:</strong> Click and drag to rotate, scroll to zoom, right-click and drag to pan.
            {isARSupported && ' Use "Enter AR Mode" to view in augmented reality.'}
          </Typography>
        </Alert>
      </CardContent>
    </Card>
  );
};

export default ARViewer;
