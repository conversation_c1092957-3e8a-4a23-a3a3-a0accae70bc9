import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Chip,
  LinearProgress,
  Divider,
  Alert
} from '@mui/material';
import {
  EcoOutlined,
  SecurityOutlined,
  MonetizationOnOutlined,
  InventoryOutlined
} from '@mui/icons-material';
import { OptimizedPackaging, EnvironmentalImpact } from '../types/packaging';

interface OptimizationResultsProps {
  packaging: OptimizedPackaging | null;
  environmentalImpact: EnvironmentalImpact | null;
  loading?: boolean;
}

const OptimizationResults: React.FC<OptimizationResultsProps> = ({
  packaging,
  environmentalImpact,
  loading = false
}) => {
  if (loading) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Optimization Results
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <LinearProgress sx={{ flexGrow: 1 }} />
            <Typography variant="body2">Optimizing...</Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (!packaging) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Optimization Results
          </Typography>
          <Alert severity="info">
            Add products and click "Optimize Packaging" to see results
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'error';
  };

  const formatMaterial = (material: string) => {
    return material.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Optimization Results
        </Typography>

        {/* Key Metrics */}
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={6} md={3}>
            <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'primary.50', borderRadius: 1 }}>
              <MonetizationOnOutlined color="primary" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h6" color="primary">
                ${packaging.estimated_cost.toFixed(2)}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Estimated Cost
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={6} md={3}>
            <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'success.50', borderRadius: 1 }}>
              <EcoOutlined color="success" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h6" color="success.main">
                {packaging.environmental_score.toFixed(0)}%
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Eco Score
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={6} md={3}>
            <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'warning.50', borderRadius: 1 }}>
              <SecurityOutlined color="warning" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h6" color="warning.main">
                {packaging.protection_level.toFixed(0)}%
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Protection Level
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={6} md={3}>
            <Box sx={{ textAlign: 'center', p: 2, bgcolor: 'info.50', borderRadius: 1 }}>
              <InventoryOutlined color="info" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="h6" color="info.main">
                {packaging.empty_space_percentage.toFixed(1)}%
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Empty Space
              </Typography>
            </Box>
          </Grid>
        </Grid>

        <Divider sx={{ my: 2 }} />

        {/* Packaging Details */}
        <Typography variant="subtitle1" gutterBottom>
          Packaging Specifications
        </Typography>
        
        <Grid container spacing={2} sx={{ mb: 2 }}>
          <Grid item xs={12} md={6}>
            <Box>
              <Typography variant="body2" color="textSecondary">
                Box Dimensions
              </Typography>
              <Typography variant="body1" fontWeight="bold">
                {packaging.box_dimensions.length} × {packaging.box_dimensions.width} × {packaging.box_dimensions.height} cm
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box>
              <Typography variant="body2" color="textSecondary">
                Material Type
              </Typography>
              <Chip
                label={formatMaterial(packaging.material_type)}
                color="primary"
                variant="outlined"
              />
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box>
              <Typography variant="body2" color="textSecondary">
                Cushioning Thickness
              </Typography>
              <Typography variant="body1">
                {packaging.cushioning_thickness} cm
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} md={6}>
            <Box>
              <Typography variant="body2" color="textSecondary">
                Material Usage
              </Typography>
              <Typography variant="body1">
                {packaging.material_usage.toFixed(0)} cm²
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {/* Performance Scores */}
        <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>
          Performance Scores
        </Typography>

        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2">Environmental Score</Typography>
            <Typography variant="body2" fontWeight="bold">
              {packaging.environmental_score.toFixed(0)}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={packaging.environmental_score}
            color={getScoreColor(packaging.environmental_score)}
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>

        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2">Protection Level</Typography>
            <Typography variant="body2" fontWeight="bold">
              {packaging.protection_level.toFixed(0)}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={packaging.protection_level}
            color={getScoreColor(packaging.protection_level)}
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>

        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2">Space Efficiency</Typography>
            <Typography variant="body2" fontWeight="bold">
              {(100 - packaging.empty_space_percentage).toFixed(0)}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={100 - packaging.empty_space_percentage}
            color={getScoreColor(100 - packaging.empty_space_percentage)}
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>

        {/* Environmental Impact */}
        {environmentalImpact && (
          <>
            <Divider sx={{ my: 2 }} />
            <Typography variant="subtitle1" gutterBottom>
              Environmental Impact
            </Typography>
            
            <Grid container spacing={2}>
              <Grid item xs={6}>
                <Box>
                  <Typography variant="body2" color="textSecondary">
                    Carbon Footprint
                  </Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {environmentalImpact.carbon_footprint.toFixed(3)} kg CO₂
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={6}>
                <Box>
                  <Typography variant="body2" color="textSecondary">
                    Waste Reduction
                  </Typography>
                  <Typography variant="body1" fontWeight="bold" color="success.main">
                    {environmentalImpact.material_waste_reduction.toFixed(1)}%
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={6}>
                <Box>
                  <Typography variant="body2" color="textSecondary">
                    Recyclability Score
                  </Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {environmentalImpact.recyclability_score}%
                  </Typography>
                </Box>
              </Grid>

              <Grid item xs={6}>
                <Box>
                  <Typography variant="body2" color="textSecondary">
                    Eco-Friendly Materials
                  </Typography>
                  <Chip
                    label={environmentalImpact.eco_friendly_materials_used ? 'Yes' : 'No'}
                    color={environmentalImpact.eco_friendly_materials_used ? 'success' : 'default'}
                    size="small"
                  />
                </Box>
              </Grid>
            </Grid>
          </>
        )}

        {/* Product Arrangement Summary */}
        <Divider sx={{ my: 2 }} />
        <Typography variant="subtitle1" gutterBottom>
          Product Arrangement
        </Typography>
        <Typography variant="body2" color="textSecondary">
          {packaging.product_arrangement.length} products optimally arranged for maximum protection and space efficiency.
        </Typography>
      </CardContent>
    </Card>
  );
};

export default OptimizationResults;
