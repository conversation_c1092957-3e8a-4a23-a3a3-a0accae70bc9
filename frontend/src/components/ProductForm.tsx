import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Typography,
  Grid,
  Chip,
  IconButton,
  Divider
} from '@mui/material';
import { Add, Delete } from '@mui/icons-material';
import { Product } from '../types/packaging';

interface ProductFormProps {
  products: Product[];
  onProductsChange: (products: Product[]) => void;
}

const ProductForm: React.FC<ProductFormProps> = ({ products, onProductsChange }) => {
  const [currentProduct, setCurrentProduct] = useState<Product>({
    name: '',
    dimensions: { length: 0, width: 0, height: 0 },
    weight: 0,
    shape: 'rectangular',
    fragility: 'medium',
    special_requirements: []
  });

  const addProduct = () => {
    if (currentProduct.name && currentProduct.weight > 0) {
      const newProduct = {
        ...currentProduct,
        id: `product_${Date.now()}`
      };
      onProductsChange([...products, newProduct]);
      setCurrentProduct({
        name: '',
        dimensions: { length: 0, width: 0, height: 0 },
        weight: 0,
        shape: 'rectangular',
        fragility: 'medium',
        special_requirements: []
      });
    }
  };

  const removeProduct = (index: number) => {
    const newProducts = products.filter((_, i) => i !== index);
    onProductsChange(newProducts);
  };

  const updateCurrentProduct = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setCurrentProduct(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof Product],
          [child]: value
        }
      }));
    } else {
      setCurrentProduct(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Product Information
      </Typography>
      
      {/* Add Product Form */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="subtitle1" gutterBottom>
            Add New Product
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Product Name"
                value={currentProduct.name}
                onChange={(e) => updateCurrentProduct('name', e.target.value)}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Weight (grams)"
                type="number"
                value={currentProduct.weight}
                onChange={(e) => updateCurrentProduct('weight', parseFloat(e.target.value) || 0)}
              />
            </Grid>
            
            <Grid item xs={4}>
              <TextField
                fullWidth
                label="Length (cm)"
                type="number"
                value={currentProduct.dimensions.length}
                onChange={(e) => updateCurrentProduct('dimensions.length', parseFloat(e.target.value) || 0)}
              />
            </Grid>
            
            <Grid item xs={4}>
              <TextField
                fullWidth
                label="Width (cm)"
                type="number"
                value={currentProduct.dimensions.width}
                onChange={(e) => updateCurrentProduct('dimensions.width', parseFloat(e.target.value) || 0)}
              />
            </Grid>
            
            <Grid item xs={4}>
              <TextField
                fullWidth
                label="Height (cm)"
                type="number"
                value={currentProduct.dimensions.height}
                onChange={(e) => updateCurrentProduct('dimensions.height', parseFloat(e.target.value) || 0)}
              />
            </Grid>
            
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Shape</InputLabel>
                <Select
                  value={currentProduct.shape}
                  onChange={(e) => updateCurrentProduct('shape', e.target.value)}
                >
                  <MenuItem value="rectangular">Rectangular</MenuItem>
                  <MenuItem value="cylindrical">Cylindrical</MenuItem>
                  <MenuItem value="spherical">Spherical</MenuItem>
                  <MenuItem value="irregular">Irregular</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={6}>
              <FormControl fullWidth>
                <InputLabel>Fragility</InputLabel>
                <Select
                  value={currentProduct.fragility}
                  onChange={(e) => updateCurrentProduct('fragility', e.target.value)}
                >
                  <MenuItem value="low">Low</MenuItem>
                  <MenuItem value="medium">Medium</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="very_high">Very High</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Product Value (USD)"
                type="number"
                value={currentProduct.value || ''}
                onChange={(e) => updateCurrentProduct('value', parseFloat(e.target.value) || undefined)}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Button
                variant="contained"
                startIcon={<Add />}
                onClick={addProduct}
                disabled={!currentProduct.name || currentProduct.weight <= 0}
              >
                Add Product
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Products List */}
      {products.length > 0 && (
        <Card>
          <CardContent>
            <Typography variant="subtitle1" gutterBottom>
              Products to Package ({products.length})
            </Typography>
            
            {products.map((product, index) => (
              <Box key={index} sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body1" fontWeight="bold">
                    {product.name}
                  </Typography>
                  <IconButton
                    color="error"
                    onClick={() => removeProduct(index)}
                    size="small"
                  >
                    <Delete />
                  </IconButton>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 1 }}>
                  <Chip
                    label={`${product.dimensions.length}×${product.dimensions.width}×${product.dimensions.height} cm`}
                    size="small"
                    variant="outlined"
                  />
                  <Chip
                    label={`${product.weight}g`}
                    size="small"
                    variant="outlined"
                  />
                  <Chip
                    label={product.shape}
                    size="small"
                    variant="outlined"
                  />
                  <Chip
                    label={`Fragility: ${product.fragility}`}
                    size="small"
                    variant="outlined"
                    color={product.fragility === 'very_high' ? 'error' : 
                           product.fragility === 'high' ? 'warning' : 'default'}
                  />
                </Box>
                
                {index < products.length - 1 && <Divider sx={{ mt: 2 }} />}
              </Box>
            ))}
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default ProductForm;
