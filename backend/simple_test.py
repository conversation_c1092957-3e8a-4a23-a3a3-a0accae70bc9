#!/usr/bin/env python3
"""
Simple test for the packaging optimization system without heavy dependencies
"""

from models.product import Product, Dimensions, PackagingRequest, FragilityLevel, ProductShape

def test_basic_functionality():
    """Test basic functionality without ML dependencies"""
    
    print("🚀 Testing AI-Powered Packaging Optimization System")
    print("=" * 60)
    
    # Create test products
    products = [
        Product(
            id="product_1",
            name="Smartphone",
            dimensions=Dimensions(length=15, width=7, height=1),
            weight=200,
            shape=ProductShape.RECTANGULAR,
            fragility=FragilityLevel.HIGH,
            value=800
        ),
        Product(
            id="product_2", 
            name="Phone Case",
            dimensions=Dimensions(length=16, width=8, height=2),
            weight=50,
            shape=ProductShape.RECTANGULAR,
            fragility=FragilityLevel.MEDIUM,
            value=25
        ),
        Product(
            id="product_3",
            name="Charging Cable",
            dimensions=Dimensions(length=20, width=5, height=2),
            weight=100,
            shape=ProductShape.RECTANGULAR,
            fragility=FragilityLevel.LOW,
            value=15
        )
    ]
    
    print(f"📦 Products to package: {len(products)}")
    for product in products:
        print(f"  - {product.name}: {product.dimensions.length}×{product.dimensions.width}×{product.dimensions.height}cm, {product.weight}g")
        print(f"    Volume: {product.volume():.1f} cm³, Fragility: {product.fragility.value}")
    
    # Test packaging request creation
    request = PackagingRequest(
        products=products,
        priority="balanced",
        shipping_distance=100
    )
    
    print(f"\n📋 Packaging Request Created:")
    print(f"  - Priority: {request.priority}")
    print(f"  - Shipping Distance: {request.shipping_distance} km")
    print(f"  - Total Products: {len(request.products)}")
    
    # Calculate basic metrics
    total_volume = sum(p.volume() for p in products)
    total_weight = sum(p.weight for p in products)
    max_fragility = max(p.fragility for p in products)
    
    print(f"\n📊 Basic Calculations:")
    print(f"  - Total Volume: {total_volume:.1f} cm³")
    print(f"  - Total Weight: {total_weight} g")
    print(f"  - Maximum Fragility: {max_fragility.value}")
    
    # Simple box dimension estimation
    max_length = max(p.dimensions.length for p in products)
    max_width = max(p.dimensions.width for p in products)
    total_height = sum(p.dimensions.height for p in products)
    
    # Add some padding
    estimated_box = {
        "length": max_length + 2,  # 2cm padding
        "width": max_width + 2,    # 2cm padding  
        "height": total_height + 3 # 3cm padding
    }
    
    box_volume = estimated_box["length"] * estimated_box["width"] * estimated_box["height"]
    space_efficiency = (total_volume / box_volume) * 100
    
    print(f"\n📐 Estimated Packaging:")
    print(f"  - Box Dimensions: {estimated_box['length']}×{estimated_box['width']}×{estimated_box['height']} cm")
    print(f"  - Box Volume: {box_volume:.1f} cm³")
    print(f"  - Space Efficiency: {space_efficiency:.1f}%")
    print(f"  - Empty Space: {100 - space_efficiency:.1f}%")
    
    # Material selection based on fragility
    if max_fragility == FragilityLevel.VERY_HIGH:
        material = "cardboard_heavy"
        cushioning = 3.0
    elif max_fragility == FragilityLevel.HIGH:
        material = "cardboard_medium"
        cushioning = 2.0
    elif max_fragility == FragilityLevel.MEDIUM:
        material = "cardboard_medium"
        cushioning = 1.0
    else:
        material = "cardboard_light"
        cushioning = 0.5
    
    print(f"\n🛡️ Protection Requirements:")
    print(f"  - Recommended Material: {material}")
    print(f"  - Cushioning Thickness: {cushioning} cm")
    
    # Simple cost estimation
    surface_area = 2 * (estimated_box["length"] * estimated_box["width"] + 
                       estimated_box["length"] * estimated_box["height"] + 
                       estimated_box["width"] * estimated_box["height"])
    
    material_cost = surface_area * 0.001  # $0.001 per cm²
    cushioning_cost = cushioning * 0.1    # $0.1 per cm thickness
    estimated_cost = material_cost + cushioning_cost
    
    print(f"\n💰 Cost Estimation:")
    print(f"  - Surface Area: {surface_area:.0f} cm²")
    print(f"  - Material Cost: ${material_cost:.2f}")
    print(f"  - Cushioning Cost: ${cushioning_cost:.2f}")
    print(f"  - Total Estimated Cost: ${estimated_cost:.2f}")
    
    # Environmental impact estimation
    carbon_factor = 0.8  # kg CO2 per kg material
    material_weight = surface_area * 0.0005  # Simplified weight calculation
    carbon_footprint = material_weight * carbon_factor
    
    print(f"\n🌱 Environmental Impact:")
    print(f"  - Estimated Material Weight: {material_weight:.3f} kg")
    print(f"  - Carbon Footprint: {carbon_footprint:.3f} kg CO₂")
    print(f"  - Recyclable Material: {'Yes' if 'cardboard' in material else 'No'}")
    
    print("\n✅ Basic functionality test completed successfully!")
    print("\n🎯 System Features Demonstrated:")
    print("  • Product data modeling and validation")
    print("  • Basic packaging calculations")
    print("  • Material selection based on fragility")
    print("  • Cost and environmental impact estimation")
    print("  • Space efficiency optimization")
    
    return True

if __name__ == "__main__":
    test_basic_functionality()
