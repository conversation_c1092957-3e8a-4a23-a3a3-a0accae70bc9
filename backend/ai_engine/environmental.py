import math
from typing import List, Dict
from models.product import Product, PackagingMaterial, EnvironmentalImpact, OptimizedPackaging

class EnvironmentalCalculator:
    """Calculate environmental impact of packaging solutions"""

    def __init__(self):
        self.material_carbon_factors = {
            PackagingMaterial.CARDBOARD_LIGHT: 0.8,  # kg CO2 per kg material
            PackagingMaterial.CARDBOARD_MEDIUM: 0.9,
            PackagingMaterial.CARDBOARD_HEAVY: 1.0,
            PackagingMaterial.BUBBLE_WRAP: 2.5,
            PackagingMaterial.FOAM: 3.2,
            PackagingMaterial.AIR_CUSHIONS: 1.8,
        }

        self.material_density = {
            PackagingMaterial.CARDBOARD_LIGHT: 0.6,  # g/cm³
            PackagingMaterial.CARDBOARD_MEDIUM: 0.8,
            PackagingMaterial.CARDBOARD_HEAVY: 1.0,
            PackagingMaterial.BUBBLE_WRAP: 0.1,
            PackagingMaterial.FOAM: 0.05,
            PackagingMaterial.AIR_CUSHIONS: 0.02,
        }

        self.decomposition_times = {
            PackagingMaterial.CARDBOARD_LIGHT: 60,  # days
            PackagingMaterial.CARDBOARD_MEDIUM: 90,
            PackagingMaterial.CARDBOARD_HEAVY: 120,
            PackagingMaterial.BUBBLE_WRAP: 3650,  # 10 years
            PackagingMaterial.FOAM: 18250,  # 50 years
            PackagingMaterial.AIR_CUSHIONS: 1825,  # 5 years
        }

        self.recyclability_scores = {
            PackagingMaterial.CARDBOARD_LIGHT: 95,
            PackagingMaterial.CARDBOARD_MEDIUM: 95,
            PackagingMaterial.CARDBOARD_HEAVY: 95,
            PackagingMaterial.BUBBLE_WRAP: 20,
            PackagingMaterial.FOAM: 10,
            PackagingMaterial.AIR_CUSHIONS: 30,
        }

    def calculate_environmental_impact(
        self,
        optimized_packaging: OptimizedPackaging,
        shipping_distance: float = 0
    ) -> EnvironmentalImpact:
        """Calculate comprehensive environmental impact"""

        # Calculate material weight
        material_weight = self._calculate_material_weight(optimized_packaging)

        # Calculate carbon footprint
        carbon_footprint = self._calculate_carbon_footprint(
            optimized_packaging.material_type,
            material_weight,
            shipping_distance
        )

        # Calculate waste reduction compared to standard packaging
        waste_reduction = self._calculate_waste_reduction(optimized_packaging)

        # Determine if eco-friendly materials are used
        eco_friendly = self._is_eco_friendly_material(optimized_packaging.material_type)

        return EnvironmentalImpact(
            carbon_footprint=carbon_footprint,
            material_waste_reduction=waste_reduction,
            recyclability_score=self.recyclability_scores[optimized_packaging.material_type],
            eco_friendly_materials_used=eco_friendly,
            estimated_decomposition_time=self.decomposition_times[optimized_packaging.material_type]
        )

    def _calculate_material_weight(self, packaging: OptimizedPackaging) -> float:
        """Calculate total weight of packaging materials in kg"""
        # Box material weight
        box_volume = (
            packaging.box_dimensions.length *
            packaging.box_dimensions.width *
            packaging.box_dimensions.height
        ) / 1000  # Convert cm³ to dm³

        material_thickness = 0.5  # cm, average thickness
        box_material_volume = packaging.material_usage * material_thickness / 1000  # dm³

        density = self.material_density[packaging.material_type]
        box_weight = box_material_volume * density / 1000  # Convert to kg

        # Cushioning weight (simplified)
        cushioning_weight = packaging.cushioning_thickness * 0.01  # kg

        return box_weight + cushioning_weight

    def _calculate_carbon_footprint(
        self,
        material: PackagingMaterial,
        material_weight: float,
        shipping_distance: float
    ) -> float:
        """Calculate total carbon footprint in kg CO2 equivalent"""

        # Material production carbon footprint
        material_carbon = material_weight * self.material_carbon_factors[material]

        # Transportation carbon footprint (simplified)
        # Assumes truck transport at 0.1 kg CO2 per kg per 100km
        transport_carbon = 0
        if shipping_distance > 0:
            transport_carbon = (material_weight * shipping_distance * 0.001)

        return material_carbon + transport_carbon

    def _calculate_waste_reduction(self, packaging: OptimizedPackaging) -> float:
        """Calculate waste reduction percentage compared to standard packaging"""

        # Estimate standard packaging waste (simplified model)
        # Assumes standard packaging has 40% empty space on average
        standard_empty_space = 40.0
        optimized_empty_space = packaging.empty_space_percentage

        if standard_empty_space <= optimized_empty_space:
            return 0.0

        reduction = ((standard_empty_space - optimized_empty_space) / standard_empty_space) * 100
        return min(reduction, 100.0)

    def _is_eco_friendly_material(self, material: PackagingMaterial) -> bool:
        """Determine if material is considered eco-friendly"""
        eco_friendly_materials = {
            PackagingMaterial.CARDBOARD_LIGHT,
            PackagingMaterial.CARDBOARD_MEDIUM,
            PackagingMaterial.CARDBOARD_HEAVY,
        }
        return material in eco_friendly_materials

    def calculate_cumulative_impact(
        self,
        impacts: List[EnvironmentalImpact],
        time_period_days: int = 365
    ) -> Dict[str, float]:
        """Calculate cumulative environmental impact over time"""

        total_carbon = sum(impact.carbon_footprint for impact in impacts)
        avg_waste_reduction = sum(impact.material_waste_reduction for impact in impacts) / len(impacts)
        avg_recyclability = sum(impact.recyclability_score for impact in impacts) / len(impacts)

        # Estimate annual impact
        packages_per_year = len(impacts) * (365 / time_period_days)
        annual_carbon = total_carbon * (365 / time_period_days)

        return {
            "total_carbon_footprint_kg": total_carbon,
            "annual_carbon_footprint_kg": annual_carbon,
            "average_waste_reduction_percent": avg_waste_reduction,
            "average_recyclability_score": avg_recyclability,
            "estimated_packages_per_year": packages_per_year,
            "carbon_savings_vs_standard_kg": annual_carbon * (avg_waste_reduction / 100)
        }

    def get_eco_recommendations(self, packaging: OptimizedPackaging) -> List[str]:
        """Get recommendations for improving environmental impact"""
        recommendations = []

        if packaging.empty_space_percentage > 30:
            recommendations.append("Consider reducing box size to minimize empty space")

        if packaging.material_type not in [
            PackagingMaterial.CARDBOARD_LIGHT,
            PackagingMaterial.CARDBOARD_MEDIUM,
            PackagingMaterial.CARDBOARD_HEAVY
        ]:
            recommendations.append("Switch to cardboard-based materials for better recyclability")

        if packaging.cushioning_thickness > 2.0:
            recommendations.append("Explore biodegradable cushioning alternatives")

        if packaging.environmental_score < 70:
            recommendations.append("Overall environmental impact can be improved")

        recommendations.append("Consider using recycled materials when available")
        recommendations.append("Implement a packaging return program for reuse")

        return recommendations
