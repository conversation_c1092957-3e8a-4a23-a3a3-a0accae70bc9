import numpy as np
from typing import List, Tuple, Dict, Any
from scipy.optimize import minimize, differential_evolution
from sklearn.cluster import KMeans
import math

from models.product import Product, Dimensions, OptimizedPackaging, PackagingMaterial, FragilityLevel

class PackagingOptimizer:
    """AI-powered packaging optimization engine"""

    def __init__(self):
        self.standard_boxes = self._load_standard_boxes()
        self.material_properties = self._load_material_properties()

    def _load_standard_boxes(self) -> List[Dict]:
        """Load standard shipping box sizes"""
        return [
            {"name": "Small", "dims": (20, 15, 10), "max_weight": 2000, "cost": 0.5},
            {"name": "Medium", "dims": (30, 25, 20), "max_weight": 5000, "cost": 0.8},
            {"name": "Large", "dims": (40, 35, 30), "max_weight": 10000, "cost": 1.2},
            {"name": "XL", "dims": (50, 45, 40), "max_weight": 20000, "cost": 1.8},
        ]

    def _load_material_properties(self) -> Dict:
        """Load packaging material properties"""
        return {
            PackagingMaterial.CARDBOARD_LIGHT: {
                "thickness": 0.3, "strength": 1.0, "cost_per_sqcm": 0.001, "eco_score": 80
            },
            PackagingMaterial.CARDBOARD_MEDIUM: {
                "thickness": 0.5, "strength": 1.5, "cost_per_sqcm": 0.0015, "eco_score": 75
            },
            PackagingMaterial.CARDBOARD_HEAVY: {
                "thickness": 0.8, "strength": 2.0, "cost_per_sqcm": 0.002, "eco_score": 70
            },
        }

    def optimize_packaging(self, products: List[Product], priority: str = "balanced") -> OptimizedPackaging:
        """Main optimization function"""

        # Calculate total volume and weight
        total_volume = sum(p.volume() for p in products)
        total_weight = sum(p.weight for p in products)

        # Determine fragility requirements
        max_fragility = max(p.fragility for p in products)

        # Find optimal box dimensions
        optimal_dims = self._optimize_box_dimensions(products, priority)

        # Select appropriate material
        material = self._select_material(max_fragility, total_weight)

        # Calculate cushioning requirements
        cushioning = self._calculate_cushioning(products, material)

        # Arrange products optimally
        arrangement = self._optimize_product_arrangement(products, optimal_dims)

        # Calculate costs and metrics
        cost = self._calculate_cost(optimal_dims, material, cushioning)
        material_usage = self._calculate_material_usage(optimal_dims, material)
        empty_space = self._calculate_empty_space(products, optimal_dims)

        return OptimizedPackaging(
            box_dimensions=Dimensions(
                length=optimal_dims[0],
                width=optimal_dims[1],
                height=optimal_dims[2]
            ),
            material_type=material,
            cushioning_thickness=cushioning,
            product_arrangement=arrangement,
            estimated_cost=cost,
            material_usage=material_usage,
            empty_space_percentage=empty_space,
            environmental_score=self._calculate_environmental_score(material, empty_space),
            protection_level=self._calculate_protection_level(material, cushioning, max_fragility)
        )

    def _optimize_box_dimensions(self, products: List[Product], priority: str) -> Tuple[float, float, float]:
        """Optimize box dimensions using genetic algorithm"""

        def objective_function(dims):
            length, width, height = dims

            # Check if all products can fit
            if not self._can_fit_products(products, dims):
                return float('inf')

            volume = length * width * height
            surface_area = 2 * (length * width + length * height + width * height)

            # Multi-objective optimization based on priority
            if priority == "cost":
                return surface_area * 0.7 + volume * 0.3
            elif priority == "environmental":
                return surface_area * 0.8 + volume * 0.2
            else:  # balanced
                return surface_area * 0.5 + volume * 0.5

        # Get bounds based on product dimensions
        min_dims = self._calculate_minimum_dimensions(products)
        max_dims = tuple(d * 2 for d in min_dims)

        bounds = [(min_dims[i], max_dims[i]) for i in range(3)]

        result = differential_evolution(objective_function, bounds, seed=42)
        return result.x

    def _can_fit_products(self, products: List[Product], box_dims: Tuple[float, float, float]) -> bool:
        """Check if products can fit in the box using 3D bin packing approximation"""
        total_volume = sum(p.volume() for p in products)
        box_volume = box_dims[0] * box_dims[1] * box_dims[2]

        # Simple volume check with packing efficiency factor
        packing_efficiency = 0.7  # Realistic packing efficiency
        return total_volume <= box_volume * packing_efficiency

    def _calculate_minimum_dimensions(self, products: List[Product]) -> Tuple[float, float, float]:
        """Calculate minimum box dimensions to fit all products"""
        max_length = max(p.dimensions.length for p in products)
        max_width = max(p.dimensions.width for p in products)
        total_height = sum(p.dimensions.height for p in products)

        return (max_length, max_width, total_height)

    def _select_material(self, fragility: FragilityLevel, weight: float) -> PackagingMaterial:
        """Select appropriate packaging material based on requirements"""
        if fragility == FragilityLevel.VERY_HIGH or weight > 15000:
            return PackagingMaterial.CARDBOARD_HEAVY
        elif fragility == FragilityLevel.HIGH or weight > 8000:
            return PackagingMaterial.CARDBOARD_MEDIUM
        else:
            return PackagingMaterial.CARDBOARD_LIGHT

    def _calculate_cushioning(self, products: List[Product], material: PackagingMaterial) -> float:
        """Calculate required cushioning thickness"""
        max_fragility = max(p.fragility for p in products)
        base_cushioning = {
            FragilityLevel.LOW: 0.5,
            FragilityLevel.MEDIUM: 1.0,
            FragilityLevel.HIGH: 2.0,
            FragilityLevel.VERY_HIGH: 3.0
        }
        return base_cushioning[max_fragility]

    def _optimize_product_arrangement(self, products: List[Product], box_dims: Tuple[float, float, float]) -> List[Dict[str, Any]]:
        """Optimize product arrangement within the box"""
        arrangement = []
        current_height = 0

        # Sort products by fragility (most fragile on top)
        sorted_products = sorted(products, key=lambda p: p.fragility.value, reverse=True)

        for i, product in enumerate(sorted_products):
            arrangement.append({
                "product_id": product.id or f"product_{i}",
                "position": {
                    "x": 0,
                    "y": 0,
                    "z": current_height
                },
                "rotation": {"x": 0, "y": 0, "z": 0}
            })
            current_height += product.dimensions.height

        return arrangement

    def _calculate_cost(self, dims: Tuple[float, float, float], material: PackagingMaterial, cushioning: float) -> float:
        """Calculate total packaging cost"""
        surface_area = 2 * (dims[0] * dims[1] + dims[0] * dims[2] + dims[1] * dims[2])
        material_cost = surface_area * self.material_properties[material]["cost_per_sqcm"]
        cushioning_cost = cushioning * 0.1  # Simplified cushioning cost
        return material_cost + cushioning_cost

    def _calculate_material_usage(self, dims: Tuple[float, float, float], material: PackagingMaterial) -> float:
        """Calculate material usage in square cm"""
        return 2 * (dims[0] * dims[1] + dims[0] * dims[2] + dims[1] * dims[2])

    def _calculate_empty_space(self, products: List[Product], box_dims: Tuple[float, float, float]) -> float:
        """Calculate percentage of empty space"""
        total_product_volume = sum(p.volume() for p in products)
        box_volume = box_dims[0] * box_dims[1] * box_dims[2]
        return ((box_volume - total_product_volume) / box_volume) * 100

    def _calculate_environmental_score(self, material: PackagingMaterial, empty_space: float) -> float:
        """Calculate environmental impact score"""
        material_score = self.material_properties[material]["eco_score"]
        efficiency_score = max(0, 100 - empty_space)
        return (material_score + efficiency_score) / 2

    def _calculate_protection_level(self, material: PackagingMaterial, cushioning: float, fragility: FragilityLevel) -> float:
        """Calculate protection level score"""
        material_strength = self.material_properties[material]["strength"]
        cushioning_factor = min(cushioning / 3.0, 1.0)  # Normalize to 0-1

        fragility_weights = {
            FragilityLevel.LOW: 0.5,
            FragilityLevel.MEDIUM: 0.7,
            FragilityLevel.HIGH: 0.9,
            FragilityLevel.VERY_HIGH: 1.0
        }

        required_protection = fragility_weights[fragility]
        provided_protection = (material_strength + cushioning_factor) / 2

        return min(provided_protection / required_protection * 100, 100)
