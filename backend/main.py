from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import List, Dict, Any
import uvicorn

from models.product import (
    Product, PackagingRequest, OptimizedPackaging,
    EnvironmentalImpact, StandardBox, PackagingMaterial
)
from ai_engine.optimizer import PackagingOptimizer
from ai_engine.environmental import EnvironmentalCalculator

app = FastAPI(
    title="AI-Powered Packaging Optimization System",
    description="Optimize packaging with AI and visualize with AR",
    version="1.0.0"
)

# Enable CORS for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize AI engines
optimizer = PackagingOptimizer()
env_calculator = EnvironmentalCalculator()

@app.get("/")
async def root():
    return {"message": "AI-Powered Packaging Optimization System API"}

@app.post("/api/optimize", response_model=OptimizedPackaging)
async def optimize_packaging(request: PackagingRequest):
    """Optimize packaging for given products"""
    try:
        if not request.products:
            raise HTTPException(status_code=400, detail="No products provided")

        # Validate products
        for product in request.products:
            if product.dimensions.length <= 0 or product.dimensions.width <= 0 or product.dimensions.height <= 0:
                raise HTTPException(status_code=400, detail="Invalid product dimensions")
            if product.weight <= 0:
                raise HTTPException(status_code=400, detail="Invalid product weight")

        # Optimize packaging
        optimized = optimizer.optimize_packaging(request.products, request.priority)

        return optimized

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Optimization failed: {str(e)}")

@app.post("/api/environmental-impact", response_model=EnvironmentalImpact)
async def calculate_environmental_impact(
    packaging: OptimizedPackaging,
    shipping_distance: float = 0
):
    """Calculate environmental impact of packaging solution"""
    try:
        impact = env_calculator.calculate_environmental_impact(packaging, shipping_distance)
        return impact
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Environmental calculation failed: {str(e)}")

@app.get("/api/materials", response_model=List[Dict[str, Any]])
async def get_available_materials():
    """Get available packaging materials and their properties"""
    materials = []
    for material in PackagingMaterial:
        materials.append({
            "name": material.value,
            "display_name": material.value.replace("_", " ").title(),
            "properties": optimizer.material_properties.get(material, {})
        })
    return materials

@app.get("/api/standard-boxes", response_model=List[StandardBox])
async def get_standard_boxes():
    """Get standard shipping box sizes"""
    boxes = []
    for box in optimizer.standard_boxes:
        boxes.append(StandardBox(
            name=box["name"],
            dimensions={
                "length": box["dims"][0],
                "width": box["dims"][1],
                "height": box["dims"][2]
            },
            max_weight=box["max_weight"],
            cost_per_unit=box["cost"]
        ))
    return boxes

@app.post("/api/batch-optimize")
async def batch_optimize(requests: List[PackagingRequest]):
    """Optimize multiple packaging requests"""
    try:
        results = []
        for request in requests:
            optimized = optimizer.optimize_packaging(request.products, request.priority)
            impact = env_calculator.calculate_environmental_impact(
                optimized,
                request.shipping_distance or 0
            )
            results.append({
                "packaging": optimized,
                "environmental_impact": impact
            })
        return results
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch optimization failed: {str(e)}")

@app.get("/api/eco-recommendations")
async def get_eco_recommendations(packaging: OptimizedPackaging):
    """Get environmental improvement recommendations"""
    try:
        recommendations = env_calculator.get_eco_recommendations(packaging)
        return {"recommendations": recommendations}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get recommendations: {str(e)}")

@app.post("/api/cumulative-impact")
async def calculate_cumulative_impact(
    impacts: List[EnvironmentalImpact],
    time_period_days: int = 365
):
    """Calculate cumulative environmental impact over time"""
    try:
        cumulative = env_calculator.calculate_cumulative_impact(impacts, time_period_days)
        return cumulative
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Cumulative calculation failed: {str(e)}")

@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "packaging-optimization-api"}

# AR-specific endpoints for 3D visualization data
@app.post("/api/ar/visualization-data")
async def get_ar_visualization_data(packaging: OptimizedPackaging):
    """Get 3D visualization data for AR rendering"""
    try:
        # Prepare 3D scene data for AR visualization
        scene_data = {
            "box": {
                "dimensions": {
                    "length": packaging.box_dimensions.length,
                    "width": packaging.box_dimensions.width,
                    "height": packaging.box_dimensions.height
                },
                "material": packaging.material_type.value,
                "color": _get_material_color(packaging.material_type)
            },
            "products": packaging.product_arrangement,
            "cushioning": {
                "thickness": packaging.cushioning_thickness,
                "type": "foam",  # Simplified
                "color": "#E0E0E0"
            },
            "metrics": {
                "empty_space": packaging.empty_space_percentage,
                "protection_level": packaging.protection_level,
                "environmental_score": packaging.environmental_score
            }
        }
        return scene_data
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"AR data generation failed: {str(e)}")

def _get_material_color(material: PackagingMaterial) -> str:
    """Get color representation for material"""
    color_map = {
        PackagingMaterial.CARDBOARD_LIGHT: "#D2B48C",
        PackagingMaterial.CARDBOARD_MEDIUM: "#CD853F",
        PackagingMaterial.CARDBOARD_HEAVY: "#A0522D",
        PackagingMaterial.BUBBLE_WRAP: "#F0F8FF",
        PackagingMaterial.FOAM: "#FFFAF0",
        PackagingMaterial.AIR_CUSHIONS: "#F5F5F5"
    }
    return color_map.get(material, "#D2B48C")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
