#!/usr/bin/env python3
"""
Simple HTTP server for the packaging optimization API
Uses only Python standard library for demonstration
"""

import json
import http.server
import socketserver
from urllib.parse import urlparse, parse_qs
from demo import SimplePackagingOptimizer, Product, Dimensions, FragilityLevel, ProductShape

class PackagingAPIHandler(http.server.BaseHTTPRequestHandler):
    
    def __init__(self, *args, **kwargs):
        self.optimizer = SimplePackagingOptimizer()
        super().__init__(*args, **kwargs)
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        
        # Add CORS headers
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        if parsed_path.path == '/':
            response = {
                "message": "AI-Powered Packaging Optimization System API",
                "version": "1.0.0",
                "endpoints": [
                    "GET / - API information",
                    "POST /api/optimize - Optimize packaging",
                    "GET /api/health - Health check",
                    "GET /api/demo - Run demo optimization"
                ]
            }
        elif parsed_path.path == '/api/health':
            response = {"status": "healthy", "service": "packaging-optimization-api"}
        elif parsed_path.path == '/api/demo':
            response = self.run_demo_optimization()
        else:
            self.send_response(404)
            response = {"error": "Endpoint not found"}
        
        self.wfile.write(json.dumps(response, indent=2).encode())
    
    def do_POST(self):
        """Handle POST requests"""
        parsed_path = urlparse(self.path)
        
        # Read request body
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            request_data = json.loads(post_data.decode('utf-8'))
        except json.JSONDecodeError:
            self.send_error(400, "Invalid JSON")
            return
        
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Content-Type', 'application/json')
        
        if parsed_path.path == '/api/optimize':
            response = self.handle_optimize_request(request_data)
        else:
            self.send_response(404)
            response = {"error": "Endpoint not found"}
        
        self.end_headers()
        self.wfile.write(json.dumps(response, indent=2).encode())
    
    def handle_optimize_request(self, request_data):
        """Handle packaging optimization request"""
        try:
            # Parse products from request
            products = []
            for product_data in request_data.get('products', []):
                dimensions = Dimensions(
                    length=product_data['dimensions']['length'],
                    width=product_data['dimensions']['width'],
                    height=product_data['dimensions']['height']
                )
                
                product = Product(
                    name=product_data['name'],
                    dimensions=dimensions,
                    weight=product_data['weight'],
                    fragility=FragilityLevel(product_data.get('fragility', 'medium')),
                    shape=ProductShape(product_data.get('shape', 'rectangular')),
                    value=product_data.get('value'),
                    product_id=product_data.get('id')
                )
                products.append(product)
            
            if not products:
                self.send_response(400)
                return {"error": "No products provided"}
            
            # Get optimization priority
            priority = request_data.get('priority', 'balanced')
            
            # Optimize packaging
            packaging_result = self.optimizer.optimize_packaging(products, priority)
            
            # Calculate environmental impact
            env_impact = self.optimizer.calculate_environmental_impact(packaging_result)
            
            # Format response
            self.send_response(200)
            return {
                "optimized_packaging": {
                    "box_dimensions": packaging_result["box_dimensions"],
                    "material_type": packaging_result["material_type"],
                    "cushioning_thickness": packaging_result["cushioning_thickness"],
                    "estimated_cost": packaging_result["estimated_cost"],
                    "empty_space_percentage": packaging_result["empty_space_percentage"],
                    "environmental_score": packaging_result["environmental_score"],
                    "protection_level": packaging_result["protection_level"]
                },
                "environmental_impact": env_impact,
                "ar_visualization_data": {
                    "box": {
                        "dimensions": packaging_result["box_dimensions"],
                        "material": packaging_result["material_type"],
                        "color": "#D2B48C"
                    },
                    "products": [
                        {
                            "product_id": p.id or f"product_{i}",
                            "position": {"x": 0, "y": 0, "z": i * 2},
                            "rotation": {"x": 0, "y": 0, "z": 0}
                        }
                        for i, p in enumerate(products)
                    ],
                    "cushioning": {
                        "thickness": packaging_result["cushioning_thickness"],
                        "type": "foam",
                        "color": "#E0E0E0"
                    },
                    "metrics": {
                        "empty_space": packaging_result["empty_space_percentage"],
                        "protection_level": packaging_result["protection_level"],
                        "environmental_score": packaging_result["environmental_score"]
                    }
                }
            }
            
        except Exception as e:
            self.send_response(500)
            return {"error": f"Optimization failed: {str(e)}"}
    
    def run_demo_optimization(self):
        """Run a demo optimization with sample products"""
        # Create sample products
        products = [
            Product(
                name="Smartphone",
                dimensions=Dimensions(15, 7, 1),
                weight=200,
                fragility=FragilityLevel.HIGH,
                value=800,
                product_id="demo_phone"
            ),
            Product(
                name="Phone Case",
                dimensions=Dimensions(16, 8, 2),
                weight=50,
                fragility=FragilityLevel.MEDIUM,
                value=25,
                product_id="demo_case"
            )
        ]
        
        # Optimize with balanced priority
        packaging_result = self.optimizer.optimize_packaging(products, "balanced")
        env_impact = self.optimizer.calculate_environmental_impact(packaging_result)
        
        return {
            "demo": True,
            "products": [
                {
                    "name": p.name,
                    "dimensions": {
                        "length": p.dimensions.length,
                        "width": p.dimensions.width,
                        "height": p.dimensions.height
                    },
                    "weight": p.weight,
                    "fragility": p.fragility.value
                }
                for p in products
            ],
            "optimization_result": packaging_result,
            "environmental_impact": env_impact
        }

def start_server(port=8000):
    """Start the simple HTTP server"""
    handler = PackagingAPIHandler
    
    with socketserver.TCPServer(("", port), handler) as httpd:
        print(f"🚀 Packaging Optimization API Server")
        print(f"🌐 Server running at http://localhost:{port}")
        print(f"📚 API Documentation:")
        print(f"   GET  / - API information")
        print(f"   POST /api/optimize - Optimize packaging")
        print(f"   GET  /api/health - Health check")
        print(f"   GET  /api/demo - Demo optimization")
        print(f"\n💡 Test the API:")
        print(f"   curl http://localhost:{port}/api/health")
        print(f"   curl http://localhost:{port}/api/demo")
        print(f"\nPress Ctrl+C to stop the server")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped")

if __name__ == "__main__":
    start_server()
