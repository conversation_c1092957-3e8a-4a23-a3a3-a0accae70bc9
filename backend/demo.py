#!/usr/bin/env python3
"""
Demo of the AI-Powered Packaging Optimization System
This version uses basic Python without external dependencies for demonstration
"""

from enum import Enum
from typing import List, Dict, Any, Optional
import json

class FragilityLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"

class ProductShape(str, Enum):
    RECTANGULAR = "rectangular"
    CYLINDRICAL = "cylindrical"
    SPHERICAL = "spherical"
    IRREGULAR = "irregular"

class PackagingMaterial(str, Enum):
    CARDBOARD_LIGHT = "cardboard_light"
    CARDBOARD_MEDIUM = "cardboard_medium"
    CARDBOARD_HEAVY = "cardboard_heavy"

class Dimensions:
    def __init__(self, length: float, width: float, height: float):
        self.length = length
        self.width = width
        self.height = height

class Product:
    def __init__(self, name: str, dimensions: Dimensions, weight: float, 
                 shape: ProductShape = ProductShape.RECTANGULAR,
                 fragility: FragilityLevel = FragilityLevel.MEDIUM,
                 value: Optional[float] = None, product_id: Optional[str] = None):
        self.id = product_id
        self.name = name
        self.dimensions = dimensions
        self.weight = weight
        self.shape = shape
        self.fragility = fragility
        self.value = value
    
    def volume(self) -> float:
        return self.dimensions.length * self.dimensions.width * self.dimensions.height

class SimplePackagingOptimizer:
    """Simplified packaging optimizer for demonstration"""
    
    def __init__(self):
        self.material_properties = {
            PackagingMaterial.CARDBOARD_LIGHT: {
                "strength": 1.0, "cost_per_sqcm": 0.001, "eco_score": 80
            },
            PackagingMaterial.CARDBOARD_MEDIUM: {
                "strength": 1.5, "cost_per_sqcm": 0.0015, "eco_score": 75
            },
            PackagingMaterial.CARDBOARD_HEAVY: {
                "strength": 2.0, "cost_per_sqcm": 0.002, "eco_score": 70
            },
        }
    
    def optimize_packaging(self, products: List[Product], priority: str = "balanced") -> Dict[str, Any]:
        """Optimize packaging for given products"""
        
        # Calculate basic metrics
        total_volume = sum(p.volume() for p in products)
        total_weight = sum(p.weight for p in products)
        max_fragility = max(p.fragility for p in products)
        
        # Calculate minimum box dimensions
        max_length = max(p.dimensions.length for p in products)
        max_width = max(p.dimensions.width for p in products)
        total_height = sum(p.dimensions.height for p in products)
        
        # Add padding based on priority
        if priority == "cost":
            padding = 1.0  # Minimal padding
        elif priority == "environmental":
            padding = 1.5  # Moderate padding
        else:  # balanced
            padding = 2.0  # Good padding
        
        box_dims = {
            "length": max_length + padding,
            "width": max_width + padding,
            "height": total_height + padding * 2
        }
        
        # Select material based on fragility
        if max_fragility == FragilityLevel.VERY_HIGH:
            material = PackagingMaterial.CARDBOARD_HEAVY
            cushioning = 3.0
        elif max_fragility == FragilityLevel.HIGH:
            material = PackagingMaterial.CARDBOARD_MEDIUM
            cushioning = 2.0
        else:
            material = PackagingMaterial.CARDBOARD_LIGHT
            cushioning = 1.0
        
        # Calculate metrics
        box_volume = box_dims["length"] * box_dims["width"] * box_dims["height"]
        empty_space = ((box_volume - total_volume) / box_volume) * 100
        
        # Calculate cost
        surface_area = 2 * (box_dims["length"] * box_dims["width"] + 
                           box_dims["length"] * box_dims["height"] + 
                           box_dims["width"] * box_dims["height"])
        
        material_cost = surface_area * self.material_properties[material]["cost_per_sqcm"]
        cushioning_cost = cushioning * 0.1
        total_cost = material_cost + cushioning_cost
        
        # Calculate scores
        environmental_score = self.material_properties[material]["eco_score"] - (empty_space * 0.5)
        protection_level = min(self.material_properties[material]["strength"] * 50, 100)
        
        return {
            "box_dimensions": box_dims,
            "material_type": material.value,
            "cushioning_thickness": cushioning,
            "estimated_cost": total_cost,
            "empty_space_percentage": empty_space,
            "environmental_score": max(environmental_score, 0),
            "protection_level": protection_level,
            "surface_area": surface_area,
            "total_volume": total_volume,
            "box_volume": box_volume
        }
    
    def calculate_environmental_impact(self, packaging_result: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate environmental impact"""
        
        # Simple carbon footprint calculation
        material_weight = packaging_result["surface_area"] * 0.0005  # kg
        carbon_footprint = material_weight * 0.8  # kg CO2
        
        # Waste reduction compared to standard packaging (40% empty space)
        standard_empty_space = 40.0
        actual_empty_space = packaging_result["empty_space_percentage"]
        waste_reduction = max(0, ((standard_empty_space - actual_empty_space) / standard_empty_space) * 100)
        
        return {
            "carbon_footprint": carbon_footprint,
            "material_waste_reduction": waste_reduction,
            "recyclability_score": 95,  # Cardboard is highly recyclable
            "eco_friendly_materials_used": True
        }

def run_demo():
    """Run the packaging optimization demo"""
    
    print("🚀 AI-Powered Packaging Optimization System Demo")
    print("=" * 60)
    
    # Create sample products
    products = [
        Product(
            name="Smartphone",
            dimensions=Dimensions(15, 7, 1),
            weight=200,
            fragility=FragilityLevel.HIGH,
            value=800,
            product_id="phone_001"
        ),
        Product(
            name="Phone Case",
            dimensions=Dimensions(16, 8, 2),
            weight=50,
            fragility=FragilityLevel.MEDIUM,
            value=25,
            product_id="case_001"
        ),
        Product(
            name="Charging Cable",
            dimensions=Dimensions(20, 5, 2),
            weight=100,
            fragility=FragilityLevel.LOW,
            value=15,
            product_id="cable_001"
        )
    ]
    
    print(f"📦 Products to Package ({len(products)} items):")
    for i, product in enumerate(products, 1):
        print(f"  {i}. {product.name}")
        print(f"     Dimensions: {product.dimensions.length}×{product.dimensions.width}×{product.dimensions.height} cm")
        print(f"     Weight: {product.weight}g, Volume: {product.volume():.1f} cm³")
        print(f"     Fragility: {product.fragility.value}, Value: ${product.value}")
        print()
    
    # Initialize optimizer
    optimizer = SimplePackagingOptimizer()
    
    # Test different optimization priorities
    priorities = ["cost", "environmental", "balanced"]
    
    for priority in priorities:
        print(f"🎯 Optimization Priority: {priority.upper()}")
        print("-" * 50)
        
        # Optimize packaging
        result = optimizer.optimize_packaging(products, priority)
        
        # Calculate environmental impact
        env_impact = optimizer.calculate_environmental_impact(result)
        
        # Display results
        print(f"📐 Optimized Box Dimensions:")
        print(f"   {result['box_dimensions']['length']:.1f} × {result['box_dimensions']['width']:.1f} × {result['box_dimensions']['height']:.1f} cm")
        
        print(f"🛡️ Packaging Specifications:")
        print(f"   Material: {result['material_type'].replace('_', ' ').title()}")
        print(f"   Cushioning: {result['cushioning_thickness']} cm")
        
        print(f"💰 Cost Analysis:")
        print(f"   Estimated Cost: ${result['estimated_cost']:.2f}")
        print(f"   Surface Area: {result['surface_area']:.0f} cm²")
        
        print(f"📊 Efficiency Metrics:")
        print(f"   Space Efficiency: {100 - result['empty_space_percentage']:.1f}%")
        print(f"   Empty Space: {result['empty_space_percentage']:.1f}%")
        print(f"   Protection Level: {result['protection_level']:.0f}%")
        print(f"   Environmental Score: {result['environmental_score']:.0f}%")
        
        print(f"🌱 Environmental Impact:")
        print(f"   Carbon Footprint: {env_impact['carbon_footprint']:.3f} kg CO₂")
        print(f"   Waste Reduction: {env_impact['material_waste_reduction']:.1f}%")
        print(f"   Recyclability: {env_impact['recyclability_score']}%")
        
        print()
    
    print("✅ Demo completed successfully!")
    print("\n🎯 Key Features Demonstrated:")
    print("  • Multi-product packaging optimization")
    print("  • Material selection based on fragility requirements")
    print("  • Cost vs. environmental impact trade-offs")
    print("  • Space efficiency optimization")
    print("  • Real-time packaging recommendations")
    print("  • Environmental impact assessment")
    
    print("\n🚀 Next Steps:")
    print("  • Install full dependencies for ML-powered optimization")
    print("  • Start the web application for AR visualization")
    print("  • Integrate with e-commerce or warehouse systems")
    print("  • Add more sophisticated 3D bin packing algorithms")

if __name__ == "__main__":
    run_demo()
