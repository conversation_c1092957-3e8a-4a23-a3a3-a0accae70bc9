from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from enum import Enum

class FragilityLevel(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"

class ProductShape(str, Enum):
    RECTANGULAR = "rectangular"
    CYLINDRICAL = "cylindrical"
    SPHERICAL = "spherical"
    IRREGULAR = "irregular"

class PackagingMaterial(str, Enum):
    CARDBOARD_LIGHT = "cardboard_light"
    CARDBOARD_MEDIUM = "cardboard_medium"
    CARDBOARD_HEAVY = "cardboard_heavy"
    BUBBLE_WRAP = "bubble_wrap"
    FOAM = "foam"
    AIR_CUSHIONS = "air_cushions"

class Dimensions(BaseModel):
    length: float = Field(..., gt=0, description="Length in cm")
    width: float = Field(..., gt=0, description="Width in cm")
    height: float = Field(..., gt=0, description="Height in cm")

class Product(BaseModel):
    id: Optional[str] = None
    name: str
    dimensions: Dimensions
    weight: float = Field(..., gt=0, description="Weight in grams")
    shape: ProductShape = ProductShape.RECTANGULAR
    fragility: FragilityLevel = FragilityLevel.MEDIUM
    value: Optional[float] = Field(None, ge=0, description="Product value in USD")
    special_requirements: Optional[List[str]] = []
    
    def volume(self) -> float:
        """Calculate product volume in cubic cm"""
        return self.dimensions.length * self.dimensions.width * self.dimensions.height

class PackagingRequest(BaseModel):
    products: List[Product]
    shipping_distance: Optional[float] = Field(None, ge=0, description="Shipping distance in km")
    priority: str = Field("balanced", description="Priority: cost, speed, environmental")
    custom_constraints: Optional[Dict[str, Any]] = {}

class OptimizedPackaging(BaseModel):
    box_dimensions: Dimensions
    material_type: PackagingMaterial
    cushioning_thickness: float = Field(..., ge=0, description="Cushioning thickness in cm")
    product_arrangement: List[Dict[str, Any]]
    estimated_cost: float
    material_usage: float = Field(..., description="Material usage in square cm")
    empty_space_percentage: float = Field(..., ge=0, le=100)
    environmental_score: float = Field(..., ge=0, le=100)
    protection_level: float = Field(..., ge=0, le=100)

class EnvironmentalImpact(BaseModel):
    carbon_footprint: float = Field(..., description="CO2 equivalent in kg")
    material_waste_reduction: float = Field(..., description="Percentage reduction vs standard")
    recyclability_score: float = Field(..., ge=0, le=100)
    eco_friendly_materials_used: bool
    estimated_decomposition_time: Optional[int] = Field(None, description="Days to decompose")

class StandardBox(BaseModel):
    name: str
    dimensions: Dimensions
    max_weight: float
    cost_per_unit: float
    availability: bool = True
