#!/usr/bin/env python3
"""
Test script for the packaging optimization system
"""

import asyncio
import json
from models.product import Product, Dimensions, PackagingRequest, FragilityLevel, ProductShape
from ai_engine.optimizer import PackagingOptimizer
from ai_engine.environmental import EnvironmentalCalculator

async def test_optimization():
    """Test the packaging optimization system"""
    
    print("🚀 Testing AI-Powered Packaging Optimization System")
    print("=" * 60)
    
    # Create test products
    products = [
        Product(
            id="product_1",
            name="Smartphone",
            dimensions=Dimensions(length=15, width=7, height=1),
            weight=200,
            shape=ProductShape.RECTANGULAR,
            fragility=FragilityLevel.HIGH,
            value=800
        ),
        Product(
            id="product_2", 
            name="Phone Case",
            dimensions=Dimensions(length=16, width=8, height=2),
            weight=50,
            shape=ProductShape.RECTANGULAR,
            fragility=FragilityLevel.MEDIUM,
            value=25
        ),
        Product(
            id="product_3",
            name="Charging Cable",
            dimensions=Dimensions(length=20, width=5, height=2),
            weight=100,
            shape=ProductShape.RECTANGULAR,
            fragility=FragilityLevel.LOW,
            value=15
        )
    ]
    
    print(f"📦 Products to package: {len(products)}")
    for product in products:
        print(f"  - {product.name}: {product.dimensions.length}×{product.dimensions.width}×{product.dimensions.height}cm, {product.weight}g")
    
    print("\n🤖 Running AI optimization...")
    
    # Initialize optimizer
    optimizer = PackagingOptimizer()
    env_calculator = EnvironmentalCalculator()
    
    # Test different priorities
    priorities = ["cost", "environmental", "balanced"]
    
    for priority in priorities:
        print(f"\n📊 Optimization Priority: {priority.upper()}")
        print("-" * 40)
        
        # Optimize packaging
        optimized = optimizer.optimize_packaging(products, priority)
        
        # Calculate environmental impact
        environmental_impact = env_calculator.calculate_environmental_impact(
            optimized, 
            shipping_distance=100  # 100km shipping
        )
        
        # Display results
        print(f"Box Dimensions: {optimized.box_dimensions.length:.1f} × {optimized.box_dimensions.width:.1f} × {optimized.box_dimensions.height:.1f} cm")
        print(f"Material: {optimized.material_type.value}")
        print(f"Estimated Cost: ${optimized.estimated_cost:.2f}")
        print(f"Empty Space: {optimized.empty_space_percentage:.1f}%")
        print(f"Environmental Score: {optimized.environmental_score:.0f}%")
        print(f"Protection Level: {optimized.protection_level:.0f}%")
        print(f"Carbon Footprint: {environmental_impact.carbon_footprint:.3f} kg CO₂")
        print(f"Waste Reduction: {environmental_impact.material_waste_reduction:.1f}%")
        
        # Get eco recommendations
        recommendations = env_calculator.get_eco_recommendations(optimized)
        if recommendations:
            print("🌱 Eco Recommendations:")
            for rec in recommendations[:3]:  # Show first 3
                print(f"  • {rec}")
    
    print("\n✅ Optimization test completed successfully!")
    print("\n🎯 Key Benefits Demonstrated:")
    print("  • AI-powered box dimension optimization")
    print("  • Material selection based on fragility")
    print("  • Environmental impact calculation")
    print("  • Multi-objective optimization (cost vs environment)")
    print("  • Real-time packaging recommendations")

if __name__ == "__main__":
    asyncio.run(test_optimization())
