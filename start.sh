#!/bin/bash

echo "🚀 Starting AI-Powered Packaging Optimization System"
echo "=================================================="

# Check if Docker is available
if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
    echo "🐳 Docker detected. Starting with Docker Compose..."
    docker-compose up --build
else
    echo "📦 Docker not available. Starting manually..."
    
    # Start backend
    echo "🔧 Starting backend server..."
    cd backend
    
    # Install Python dependencies if needed
    if [ ! -d "venv" ]; then
        echo "Creating Python virtual environment..."
        python3 -m venv venv
        source venv/bin/activate
        pip install -r requirements.txt
    else
        source venv/bin/activate
    fi
    
    # Start backend server in background
    uvicorn main:app --host 0.0.0.0 --port 8000 --reload &
    BACKEND_PID=$!
    
    cd ..
    
    # Start frontend
    echo "🎨 Starting frontend server..."
    cd frontend
    
    # Install Node.js dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo "Installing Node.js dependencies..."
        npm install
    fi
    
    # Start frontend server
    npm start &
    FRONTEND_PID=$!
    
    cd ..
    
    echo "✅ System started successfully!"
    echo "🌐 Frontend: http://localhost:3000"
    echo "🔧 Backend API: http://localhost:8000"
    echo "📚 API Documentation: http://localhost:8000/docs"
    echo ""
    echo "Press Ctrl+C to stop all services"
    
    # Wait for interrupt
    trap "echo 'Stopping services...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT
    wait
fi
